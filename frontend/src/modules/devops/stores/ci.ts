/**
 * CI 任务状态管理
 * 管理持续集成任务的状态和操作
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { DevOpsCiTask, DevOpsCiTaskInstance, QueryParams } from '@/modules/devops/types/devops'

// API 服务接口
interface CiApiService {
  getTasks(params?: QueryParams): Promise<DevOpsCiTask[]>
  getTaskById(id: number): Promise<DevOpsCiTask>
  createTask(task: CreateCiTaskRequest): Promise<DevOpsCiTask>
  updateTask(id: number, task: UpdateCiTaskRequest): Promise<DevOpsCiTask>
  deleteTask(id: number): Promise<void>
  executeTask(id: number, params?: any): Promise<DevOpsCiTaskInstance>
  stopTaskInstance(instanceId: string): Promise<void>
  getTaskLogs(instanceId: string): Promise<string>
  getTaskInstances(taskId: number): Promise<DevOpsCiTaskInstance[]>
}

// 请求类型定义
export interface CreateCiTaskRequest {
  name: string
  description?: string
  componentId: number
  taskType: string
  triggerType: string
  schedule?: string
  timeout?: number
  configuration: {
    workingDirectory: string
    environment: string
    script: string
    notifyOnSuccess: boolean
    notifyOnFailure: boolean
    notificationChannels: string[]
  }
}

export interface UpdateCiTaskRequest extends Partial<CreateCiTaskRequest> {
  status?: string
}

// 模拟 API 服务
const createMockApiService = (): CiApiService => ({
  async getTasks(_params?: QueryParams): Promise<DevOpsCiTask[]> {
    // 模拟 API 延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    return [
      {
        id: 1,
        name: '用户认证服务构建',
        description: '用户认证服务的持续集成任务',
        componentId: 1,
        taskType: 'build',
        triggerType: 'push',
        status: 'ACTIVE',
        timeout: 30,
        configuration: {
          workingDirectory: '/workspace',
          environment: 'NODE_ENV=production',
          script: 'npm ci && npm run build',
          notifyOnSuccess: false,
          notifyOnFailure: true,
          notificationChannels: ['email']
        },
        userId: 1,
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T10:30:00Z'
      },
      {
        id: 2,
        name: '订单处理引擎测试',
        description: '订单处理引擎的单元测试任务',
        componentId: 2,
        taskType: 'test',
        triggerType: 'manual',
        status: 'RUNNING',
        timeout: 45,
        configuration: {
          workingDirectory: '/workspace',
          environment: 'NODE_ENV=test',
          script: 'npm test -- --coverage',
          notifyOnSuccess: true,
          notifyOnFailure: true,
          notificationChannels: ['email', 'slack']
        },
        userId: 1,
        createdAt: '2024-01-15T09:15:00Z',
        updatedAt: '2024-01-15T11:00:00Z'
      }
    ]
  },

  async getTaskById(id: number): Promise<DevOpsCiTask> {
    const tasks = await this.getTasks()
    const task = tasks.find(t => t.id === id)
    if (!task) {
      throw new Error(`CI 任务 ${id} 不存在`)
    }
    return task
  },

  async createTask(task: CreateCiTaskRequest): Promise<DevOpsCiTask> {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    return {
      id: Date.now(),
      ...task,
      status: 'ACTIVE',
      userId: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  },

  async updateTask(id: number, task: UpdateCiTaskRequest): Promise<DevOpsCiTask> {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const existingTask = await this.getTaskById(id)
    return {
      ...existingTask,
      ...task,
      updatedAt: new Date().toISOString()
    }
  },

  async deleteTask(_id: number): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 模拟删除操作
  },

  async executeTask(id: number, _params?: any): Promise<DevOpsCiTaskInstance> {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return {
      id: Date.now(),
      ciTaskId: id,
      instanceId: `ci-${Date.now()}`,
      status: 'RUNNING',
      startTime: new Date().toISOString(),
      logs: '',
      userId: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  },

  async stopTaskInstance(_instanceId: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 模拟停止操作
  },

  async getTaskLogs(_instanceId: string): Promise<string> {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return `2024-01-15T10:30:00Z INFO 开始执行 CI 任务...
2024-01-15T10:30:05Z INFO 正在拉取代码...
2024-01-15T10:30:10Z INFO 代码拉取完成
2024-01-15T10:30:15Z INFO 开始构建...
2024-01-15T10:32:00Z WARN 发现警告: 未使用的导入
2024-01-15T10:34:30Z INFO 构建完成
2024-01-15T10:35:00Z INFO 任务执行成功`
  },

  async getTaskInstances(taskId: number): Promise<DevOpsCiTaskInstance[]> {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return [
      {
        id: 1,
        ciTaskId: taskId,
        instanceId: `ci-${taskId}-001`,
        status: 'COMPLETED',
        startTime: '2024-01-15T10:30:00Z',
        endTime: '2024-01-15T10:35:00Z',
        logs: '',
        userId: 1,
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T10:35:00Z'
      }
    ]
  }
})

export const useCiStore = defineStore('ci', () => {
  // API 服务实例
  const apiService = createMockApiService()

  // 状态定义
  const tasks = ref<DevOpsCiTask[]>([])
  const currentTask = ref<DevOpsCiTask | null>(null)
  const taskInstances = ref<DevOpsCiTaskInstance[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const activeTasks = computed(() => 
    tasks.value.filter(task => task.status === 'ACTIVE')
  )
  
  const runningTasks = computed(() =>
    tasks.value.filter(task => task.status === 'RUNNING')
  )

  const completedTasks = computed(() =>
    tasks.value.filter(task => task.status === 'COMPLETED')
  )

  const failedTasks = computed(() =>
    tasks.value.filter(task => task.status === 'FAILED')
  )

  const tasksByType = computed(() => {
    const result: Record<string, DevOpsCiTask[]> = {}
    tasks.value.forEach(task => {
      if (!result[task.taskType]) {
        result[task.taskType] = []
      }
      result[task.taskType].push(task)
    })
    return result
  })

  const tasksByComponent = computed(() => {
    const result: Record<number, DevOpsCiTask[]> = {}
    tasks.value.forEach(task => {
      if (!result[task.componentId]) {
        result[task.componentId] = []
      }
      result[task.componentId].push(task)
    })
    return result
  })

  // 操作方法
  const fetchTasks = async (params?: QueryParams) => {
    loading.value = true
    error.value = null
    try {
      const response = await apiService.getTasks(params)
      tasks.value = response
    } catch (err: any) {
      error.value = err.message || '获取 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchTaskById = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      const task = await apiService.getTaskById(id)
      currentTask.value = task
      return task
    } catch (err: any) {
      error.value = err.message || '获取 CI 任务详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const createTask = async (taskData: CreateCiTaskRequest) => {
    loading.value = true
    error.value = null
    try {
      const newTask = await apiService.createTask(taskData)
      tasks.value.unshift(newTask)
      return newTask
    } catch (err: any) {
      error.value = err.message || '创建 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateTask = async (id: number, taskData: UpdateCiTaskRequest) => {
    loading.value = true
    error.value = null
    try {
      const updatedTask = await apiService.updateTask(id, taskData)
      const index = tasks.value.findIndex(t => t.id === id)
      if (index !== -1) {
        tasks.value[index] = updatedTask
      }
      if (currentTask.value?.id === id) {
        currentTask.value = updatedTask
      }
      return updatedTask
    } catch (err: any) {
      error.value = err.message || '更新 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteTask = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      await apiService.deleteTask(id)
      const index = tasks.value.findIndex(t => t.id === id)
      if (index !== -1) {
        tasks.value.splice(index, 1)
      }
      if (currentTask.value?.id === id) {
        currentTask.value = null
      }
    } catch (err: any) {
      error.value = err.message || '删除 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const executeTask = async (id: number, params?: any) => {
    loading.value = true
    error.value = null
    try {
      const instance = await apiService.executeTask(id, params)
      taskInstances.value.unshift(instance)
      
      // 更新任务状态为运行中
      const taskIndex = tasks.value.findIndex(t => t.id === id)
      if (taskIndex !== -1) {
        tasks.value[taskIndex] = {
          ...tasks.value[taskIndex],
          status: 'RUNNING',
          updatedAt: new Date().toISOString()
        }
      }
      
      return instance
    } catch (err: any) {
      error.value = err.message || '执行 CI 任务失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const stopTaskInstance = async (instanceId: string) => {
    loading.value = true
    error.value = null
    try {
      await apiService.stopTaskInstance(instanceId)
      
      // 更新实例状态
      const instanceIndex = taskInstances.value.findIndex(i => i.instanceId === instanceId)
      if (instanceIndex !== -1) {
        taskInstances.value[instanceIndex] = {
          ...taskInstances.value[instanceIndex],
          status: 'STOPPED',
          endTime: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }
    } catch (err: any) {
      error.value = err.message || '停止 CI 任务实例失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchTaskLogs = async (instanceId: string) => {
    try {
      return await apiService.getTaskLogs(instanceId)
    } catch (err: any) {
      error.value = err.message || '获取任务日志失败'
      throw err
    }
  }

  const fetchTaskInstances = async (taskId: number) => {
    loading.value = true
    error.value = null
    try {
      const instances = await apiService.getTaskInstances(taskId)
      taskInstances.value = instances
      return instances
    } catch (err: any) {
      error.value = err.message || '获取任务实例失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const resetState = () => {
    tasks.value = []
    currentTask.value = null
    taskInstances.value = []
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    tasks,
    currentTask,
    taskInstances,
    loading,
    error,
    
    // 计算属性
    activeTasks,
    runningTasks,
    completedTasks,
    failedTasks,
    tasksByType,
    tasksByComponent,
    
    // 方法
    fetchTasks,
    fetchTaskById,
    createTask,
    updateTask,
    deleteTask,
    executeTask,
    stopTaskInstance,
    fetchTaskLogs,
    fetchTaskInstances,
    clearError,
    resetState
  }
})
