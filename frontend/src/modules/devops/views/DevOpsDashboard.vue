<template>
  <div class="devops-dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="header-title">
          <h1>DevOps 管理平台</h1>
          <p>统一的项目、应用、组件和资源管理平台</p>
        </div>
        <div class="header-actions">
          <button @click="refreshStats" :disabled="loading" class="refresh-btn">
            <svg class="refresh-icon" :class="{ spinning: loading }" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
            </svg>
            刷新
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon project-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.projects || 0 }}</div>
          <div class="stat-label">项目</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon application-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.applications || 0 }}</div>
          <div class="stat-label">应用</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon component-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9 12a1 1 0 002 0V6.414l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 6.414V12zM3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.components || 0 }}</div>
          <div class="stat-label">组件</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon resource-icon">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path d="M3 7v10a2 2 0 002 2h14l-2-2H5V7H3zM14 2H8a2 2 0 00-2 2v8a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2zM8 4h6v8H8V4z" />
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.resources || 0 }}</div>
          <div class="stat-label">资源</div>
        </div>
      </div>
    </div>

    <!-- 主要功能模块 -->
    <div class="modules-grid">
      <!-- 项目管理 -->
      <div class="module-card" @click="navigateTo('/devops/projects')">
        <div class="module-header">
          <div class="module-icon project-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
            </svg>
          </div>
          <h3>项目管理</h3>
        </div>
        <p>管理和组织您的项目，创建项目层级结构，配置项目设置和权限。</p>
        <div class="module-actions">
          <span class="action-link">管理项目 →</span>
        </div>
      </div>

      <!-- 应用管理 -->
      <div class="module-card" @click="navigateTo('/devops/applications')">
        <div class="module-header">
          <div class="module-icon application-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
            </svg>
          </div>
          <h3>应用管理</h3>
        </div>
        <p>管理项目中的应用程序，配置应用环境，监控应用状态和性能。</p>
        <div class="module-actions">
          <span class="action-link">管理应用 →</span>
        </div>
      </div>

      <!-- 组件管理 -->
      <div class="module-card" @click="navigateTo('/devops/components')">
        <div class="module-header">
          <div class="module-icon component-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M9 12a1 1 0 002 0V6.414l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 6.414V12zM3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <h3>组件管理</h3>
        </div>
        <p>管理应用的各个组件，配置代码仓库，设置构建和部署流程。</p>
        <div class="module-actions">
          <span class="action-link">管理组件 →</span>
        </div>
      </div>

      <!-- 资源管理 -->
      <div class="module-card" @click="navigateTo('/devops/resources')">
        <div class="module-header">
          <div class="module-icon resource-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M3 7v10a2 2 0 002 2h14l-2-2H5V7H3zM14 2H8a2 2 0 00-2 2v8a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2zM8 4h6v8H8V4z" />
            </svg>
          </div>
          <h3>资源管理</h3>
        </div>
        <p>管理组件相关的资源，包括数据库、缓存、存储等基础设施资源。</p>
        <div class="module-actions">
          <span class="action-link">管理资源 →</span>
        </div>
      </div>

      <!-- CI/CD管理 -->
      <div class="module-card" @click="navigateTo('/devops/ci-cd')">
        <div class="module-header">
          <div class="module-icon cicd-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clip-rule="evenodd" />
            </svg>
          </div>
          <h3>CI/CD 管理</h3>
        </div>
        <p>配置和管理持续集成和持续部署流水线，自动化构建、测试和部署流程。</p>
        <div class="module-actions">
          <span class="action-link">管理流水线 →</span>
        </div>
      </div>

      <!-- 监控中心 -->
      <div class="module-card" @click="navigateTo('/devops/monitoring')">
        <div class="module-header">
          <div class="module-icon monitoring-icon">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3>监控中心</h3>
        </div>
        <p>实时监控系统状态，查看任务执行历史，分析性能指标和日志。</p>
        <div class="module-actions">
          <span class="action-link">查看监控 →</span>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <div class="activity-header">
        <h2>最近活动</h2>
        <router-link to="/devops/activity" class="view-all-link">查看全部</router-link>
      </div>
      <div class="activity-list">
        <div v-if="recentActivities.length === 0" class="no-activity">
          <svg class="no-activity-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
          </svg>
          <p>暂无最近活动</p>
        </div>
        <div
          v-else
          v-for="activity in recentActivities"
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon" :class="getActivityIconClass(activity.type)">
            <svg viewBox="0 0 20 20" fill="currentColor">
              <path :d="getActivityIconPath(activity.type)" />
            </svg>
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-description">{{ activity.description }}</div>
            <div class="activity-time">{{ formatTime(activity.createdAt) }}</div>
          </div>
          <div class="activity-status">
            <StatusTag :status="activity.status" size="small" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import StatusTag from '@/components/StatusTag.vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const stats = ref({
  projects: 0,
  applications: 0,
  components: 0,
  resources: 0
})

const recentActivities = ref([
  {
    id: 1,
    type: 'ci',
    title: 'CI任务执行完成',
    description: '项目 "电商平台" 的构建任务已成功完成',
    status: 'COMPLETED',
    createdAt: new Date(Date.now() - 1000 * 60 * 30) // 30分钟前
  },
  {
    id: 2,
    type: 'cd',
    title: '应用部署成功',
    description: '应用 "用户服务" 已成功部署到生产环境',
    status: 'SUCCESS',
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2小时前
  },
  {
    id: 3,
    type: 'project',
    title: '新项目创建',
    description: '项目 "移动应用" 已创建并初始化',
    status: 'ACTIVE',
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24) // 1天前
  }
])

// 方法
const navigateTo = (path: string) => {
  router.push(path)
}

const refreshStats = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据更新
    stats.value = {
      projects: Math.floor(Math.random() * 50) + 10,
      applications: Math.floor(Math.random() * 100) + 20,
      components: Math.floor(Math.random() * 200) + 50,
      resources: Math.floor(Math.random() * 300) + 100
    }
  } finally {
    loading.value = false
  }
}

const getActivityIconClass = (type: string) => {
  const classes = {
    ci: 'ci-activity',
    cd: 'cd-activity',
    project: 'project-activity',
    application: 'application-activity',
    component: 'component-activity',
    resource: 'resource-activity'
  }
  return classes[type as keyof typeof classes] || 'default-activity'
}

const getActivityIconPath = (type: string) => {
  const paths = {
    ci: 'M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm6 5a1 1 0 10-2 0v6a1 1 0 102 0V7z',
    cd: 'M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm1 5a1 1 0 000 2h4.586l-2.293 2.293a1 1 0 001.414 1.414l4-4a1 1 0 000-1.414l-4-4a1 1 0 10-1.414 1.414L9.586 7H5z',
    project: 'M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z',
    application: 'M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z',
    component: 'M9 12a1 1 0 002 0V6.414l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 6.414V12zM3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z',
    resource: 'M3 7v10a2 2 0 002 2h14l-2-2H5V7H3zM14 2H8a2 2 0 00-2 2v8a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2zM8 4h6v8H8V4z'
  }
  return paths[type as keyof typeof paths] || paths.project
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}

// 生命周期
onMounted(() => {
  refreshStats()
})
</script>

<style scoped>
.devops-dashboard {
  @apply min-h-screen bg-gray-50 p-6;
}

/* 页面头部 */
.dashboard-header {
  @apply mb-8;
}

.header-content {
  @apply flex items-center justify-between;
}

.header-title h1 {
  @apply text-3xl font-bold text-gray-900 m-0;
}

.header-title p {
  @apply text-gray-600 mt-1 m-0;
}

.header-actions {
  @apply flex items-center gap-3;
}

.refresh-btn {
  @apply flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50;
}

.refresh-icon {
  @apply w-4 h-4;
}

.refresh-icon.spinning {
  @apply animate-spin;
}

/* 统计卡片 */
.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8;
}

.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 flex items-center gap-4;
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
}

.stat-icon svg {
  @apply w-6 h-6;
}

.stat-icon.project-icon {
  @apply bg-blue-100 text-blue-600;
}

.stat-icon.application-icon {
  @apply bg-green-100 text-green-600;
}

.stat-icon.component-icon {
  @apply bg-purple-100 text-purple-600;
}

.stat-icon.resource-icon {
  @apply bg-orange-100 text-orange-600;
}

.stat-content {
  @apply flex-1;
}

.stat-number {
  @apply text-2xl font-bold text-gray-900;
}

.stat-label {
  @apply text-sm text-gray-600 mt-1;
}

/* 模块网格 */
.modules-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8;
}

.module-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 cursor-pointer transition-all hover:shadow-md hover:border-gray-300;
}

.module-header {
  @apply flex items-center gap-3 mb-3;
}

.module-icon {
  @apply w-10 h-10 rounded-lg flex items-center justify-center;
}

.module-icon svg {
  @apply w-5 h-5;
}

.module-icon.cicd-icon {
  @apply bg-indigo-100 text-indigo-600;
}

.module-icon.monitoring-icon {
  @apply bg-emerald-100 text-emerald-600;
}

.module-header h3 {
  @apply text-lg font-semibold text-gray-900 m-0;
}

.module-card p {
  @apply text-gray-600 text-sm leading-relaxed mb-4 m-0;
}

.module-actions {
  @apply flex items-center justify-between;
}

.action-link {
  @apply text-blue-600 text-sm font-medium hover:text-blue-700 transition-colors;
}

/* 最近活动 */
.recent-activity {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.activity-header {
  @apply flex items-center justify-between mb-4;
}

.activity-header h2 {
  @apply text-xl font-semibold text-gray-900 m-0;
}

.view-all-link {
  @apply text-blue-600 text-sm font-medium hover:text-blue-700 transition-colors;
}

.activity-list {
  @apply space-y-4;
}

.no-activity {
  @apply flex flex-col items-center justify-center py-8 text-gray-500;
}

.no-activity-icon {
  @apply w-12 h-12 mb-2;
}

.activity-item {
  @apply flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 transition-colors;
}

.activity-icon {
  @apply w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0;
}

.activity-icon svg {
  @apply w-5 h-5;
}

.activity-icon.ci-activity {
  @apply bg-blue-100 text-blue-600;
}

.activity-icon.cd-activity {
  @apply bg-green-100 text-green-600;
}

.activity-icon.project-activity {
  @apply bg-purple-100 text-purple-600;
}

.activity-icon.application-activity {
  @apply bg-orange-100 text-orange-600;
}

.activity-icon.component-activity {
  @apply bg-indigo-100 text-indigo-600;
}

.activity-icon.resource-activity {
  @apply bg-pink-100 text-pink-600;
}

.activity-icon.default-activity {
  @apply bg-gray-100 text-gray-600;
}

.activity-content {
  @apply flex-1;
}

.activity-title {
  @apply font-medium text-gray-900 text-sm;
}

.activity-description {
  @apply text-gray-600 text-sm mt-1;
}

.activity-time {
  @apply text-gray-400 text-xs mt-1;
}

.activity-status {
  @apply flex-shrink-0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .devops-dashboard {
    @apply p-4;
  }

  .header-content {
    @apply flex-col items-start gap-4;
  }

  .stats-grid {
    @apply grid-cols-2 gap-4;
  }

  .modules-grid {
    @apply grid-cols-1 gap-4;
  }

  .stat-card {
    @apply p-4;
  }

  .module-card {
    @apply p-4;
  }

  .activity-item {
    @apply flex-col items-start gap-2;
  }

  .activity-status {
    @apply self-end;
  }
}

/* 动画效果 */
.module-card:hover .action-link {
  @apply transform translate-x-1;
}

.stat-card:hover {
  @apply transform -translate-y-1;
}

/* 加载状态 */
.refresh-btn:disabled {
  @apply cursor-not-allowed;
}
</style>
